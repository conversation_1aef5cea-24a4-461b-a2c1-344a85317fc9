"""
Lifecycle-bound ModelRegistry for addon operations

This module provides a temporary ModelRegistry that is created during addon
installation, upgrade, and uninstallation processes. It discovers models
from a specific addon and provides them for schema synchronization and
IR metadata population.

The ModelRegistry is NOT a global singleton - it's created per addon operation
and disposed of when the operation completes.
"""
import importlib
import inspect
import os
import sys
from typing import Dict, List, Optional, Type, Any, Set
from pathlib import Path

from ..logging import get_logger
from .base import BaseModel


class ModelRegistry:
    """
    Lifecycle-bound model registry for addon operations
    
    This registry is temporary and addon-specific. It's created when an addon
    operation begins and disposed of when the operation completes.
    
    Key characteristics:
    - Lifecycle-bound: Only exists during addon operations
    - Temporary scope: Created and disposed per operation
    - Addon-specific: Tracks models only for the specific addon being processed
    """
    
    def __init__(self, addon_name: str):
        """
        Initialize registry for a specific addon
        
        Args:
            addon_name: Name of the addon to discover models for
        """
        self.addon_name = addon_name
        self.logger = get_logger(f"{__name__}.{addon_name}")
        self._models: Dict[str, Type[BaseModel]] = {}
        self._discovered = False
        
        self.logger.debug(f"Created ModelRegistry for addon: {addon_name}")
    
    def discover_models(self) -> None:
        """
        Discover all models in the addon by recursively scanning all Python modules
        within the addon directory for BaseModel subclasses
        """
        if self._discovered:
            self.logger.debug(f"Models already discovered for addon: {self.addon_name}")
            return

        self.logger.info(f"Discovering models for addon: {self.addon_name}")

        try:
            self._discover_addon_models_recursive()
            self._discovered = True
            self.logger.info(f"Discovered {len(self._models)} models for addon: {self.addon_name}")

        except Exception as e:
            self.logger.error(f"Failed to discover models for addon {self.addon_name}: {e}")
            raise
    
    def _discover_addon_models_recursive(self) -> None:
        """
        Discover models by recursively scanning all Python modules within the addon directory
        """
        # Try different addon import paths
        addon_module_paths = [
            f"addons.{self.addon_name}",
            f"erp.addons.{self.addon_name}"
        ]

        addon_package = None
        addon_module_path = None

        for path in addon_module_paths:
            try:
                addon_package = importlib.import_module(path)
                addon_module_path = path
                break
            except ImportError:
                continue

        if not addon_package:
            self.logger.warning(f"Addon {self.addon_name} not found in any import path")
            return

        # Get the addon directory path
        addon_dir = Path(addon_package.__file__).parent
        self.logger.debug(f"Scanning addon directory: {addon_dir}")

        # Recursively scan all Python files in the addon directory
        visited_modules = set()
        self._scan_directory_recursive(addon_dir, addon_module_path, visited_modules)

    def _scan_directory_recursive(self, directory: Path, base_module_path: str, visited_modules: set) -> None:
        """
        Recursively scan a directory for Python modules containing BaseModel subclasses

        Args:
            directory: Directory path to scan
            base_module_path: Base module path for imports
            visited_modules: Set of already visited module names to avoid duplicates
        """
        try:
            # Find all Python files in the directory and subdirectories
            for py_file in directory.rglob("*.py"):
                # Skip files that start with __ (like __init__.py, __pycache__, etc.)
                if py_file.name.startswith("__"):
                    continue

                # Skip hidden files and directories
                if any(part.startswith('.') for part in py_file.parts):
                    continue

                # Skip __pycache__ directories
                if '__pycache__' in py_file.parts:
                    continue

                # Calculate relative path from the addon directory
                relative_path = py_file.relative_to(directory)

                # Convert file path to module name
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                module_name = f"{base_module_path}.{'.'.join(module_parts)}"

                # Skip if already visited
                if module_name in visited_modules:
                    continue

                visited_modules.add(module_name)

                # Try to import and scan the module
                try:
                    module = importlib.import_module(module_name)
                    self._scan_module_for_models(module)
                    self.logger.debug(f"Scanned module: {module_name}")
                except Exception as e:
                    self.logger.debug(f"Failed to import {module_name}: {e}")

        except Exception as e:
            self.logger.warning(f"Error scanning directory {directory}: {e}")
    
    def _scan_module_for_models(self, module) -> None:
        """
        Scan a module for BaseModel subclasses

        Args:
            module: Python module to scan
        """
        for name in dir(module):
            obj = getattr(module, name)

            # Check if it's a class that inherits from BaseModel
            if (inspect.isclass(obj) and
                issubclass(obj, BaseModel) and
                obj is not BaseModel and
                # Exclude the base hierarchy classes themselves
                obj.__name__ not in ('AbstractModel', 'TransientModel', 'Model') and
                hasattr(obj, '_name') and
                obj._name):

                model_name = obj._name
                self._models[model_name] = obj

                # Log model type for debugging
                model_type = "Abstract" if getattr(obj, '_abstract', False) else \
                           "Transient" if getattr(obj, '_transient', False) else \
                           "Standard"
                self.logger.debug(f"Discovered {model_type} model: {model_name} ({obj.__name__})")
    
    def all(self) -> Dict[str, Type[BaseModel]]:
        """
        Get all discovered models

        Returns:
            Dictionary mapping model names to model classes
        """
        if not self._discovered:
            self.discover_models()

        return self._models.copy()

    def get_models_by_type(self, model_type: str = 'standard') -> Dict[str, Type[BaseModel]]:
        """
        Get models filtered by type

        Args:
            model_type: Type of models to return ('standard', 'abstract', 'transient', 'all')

        Returns:
            Dictionary mapping model names to model classes of the specified type
        """
        if not self._discovered:
            self.discover_models()

        if model_type == 'all':
            return self._models.copy()

        filtered_models = {}
        for model_name, model_class in self._models.items():
            if model_type == 'abstract' and getattr(model_class, '_abstract', False):
                filtered_models[model_name] = model_class
            elif model_type == 'transient' and getattr(model_class, '_transient', False):
                filtered_models[model_name] = model_class
            elif model_type == 'standard' and not getattr(model_class, '_abstract', False) and not getattr(model_class, '_transient', False):
                filtered_models[model_name] = model_class

        return filtered_models
    
    def get(self, model_name: str) -> Optional[Type[BaseModel]]:
        """
        Get a specific model by name
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            Model class or None if not found
        """
        if not self._discovered:
            self.discover_models()
        
        return self._models.get(model_name)
    
    def get_model_names(self) -> List[str]:
        """
        Get list of all model names in this addon

        Returns:
            List of model names
        """
        if not self._discovered:
            self.discover_models()

        return list(self._models.keys())

    def get_model_fields(self, model_name: str) -> Dict[str, Any]:
        """
        Get all fields for a specific model

        Args:
            model_name: Technical name of the model

        Returns:
            Dictionary mapping field names to field objects
        """
        model_class = self.get(model_name)
        if not model_class:
            return {}

        return getattr(model_class, '_fields', {})

    def get_all_models_with_fields(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all models with their field information

        Returns:
            Dictionary mapping model names to their field dictionaries
        """
        if not self._discovered:
            self.discover_models()

        result = {}
        for model_name, model_class in self._models.items():
            result[model_name] = {
                'class': model_class,
                'fields': getattr(model_class, '_fields', {}),
                'table': getattr(model_class, '_table', None) or model_name.replace('.', '_'),
                'description': getattr(model_class, '_description', None) or model_name
            }

        return result
    
    def has_models(self) -> bool:
        """
        Check if this addon has any models
        
        Returns:
            True if addon has models, False otherwise
        """
        if not self._discovered:
            self.discover_models()
        
        return len(self._models) > 0
    
    def clear(self) -> None:
        """
        Clear the registry (for cleanup)
        """
        self._models.clear()
        self._discovered = False
        self.logger.debug(f"Cleared ModelRegistry for addon: {self.addon_name}")
    
    def __len__(self) -> int:
        """Get number of models in registry"""
        if not self._discovered:
            self.discover_models()
        return len(self._models)
    
    def __contains__(self, model_name: str) -> bool:
        """Check if model exists in registry"""
        if not self._discovered:
            self.discover_models()
        return model_name in self._models
    
    def __repr__(self) -> str:
        """String representation"""
        return f"<ModelRegistry(addon={self.addon_name}, models={len(self._models)})>"


def create_addon_model_registry(addon_name: str) -> ModelRegistry:
    """
    Factory function to create a ModelRegistry for a specific addon

    Args:
        addon_name: Name of the addon to create registry for

    Returns:
        ModelRegistry instance for the addon
    """
    return ModelRegistry(addon_name)