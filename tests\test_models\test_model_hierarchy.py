"""
Test suite for Model Hierarchy functionality

This module tests:
- BaseModel as the core ORM class (not used directly)
- AbstractModel for reusable logic without DB table
- TransientModel for temporary data with auto-cleanup
- Model for persistent business models
"""
import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from erp.models import BaseModel, AbstractModel, TransientModel, Model
from erp.fields import Char, Text, Integer, Boolean, Datetime


class TestModelHierarchy:
    """Test the model hierarchy structure and behavior"""

    def test_model_hierarchy_inheritance(self):
        """Test that all model types properly inherit from BaseModel"""
        # Test inheritance chain
        assert issubclass(AbstractModel, BaseModel)
        assert issubclass(TransientModel, BaseModel)
        assert issubclass(Model, BaseModel)
        
        # Test that they are different classes
        assert AbstractModel is not BaseModel
        assert TransientModel is not BaseModel
        assert Model is not BaseModel
        assert AbstractModel is not TransientModel
        assert AbstractModel is not Model
        assert TransientModel is not Model

    def test_model_type_attributes(self):
        """Test that each model type has correct attributes"""
        # AbstractModel attributes
        assert AbstractModel._auto_create_table is False
        assert AbstractModel._abstract is True
        
        # TransientModel attributes
        assert TransientModel._transient is True
        assert TransientModel._transient_max_hours == 1
        assert TransientModel._auto_create_table is True
        
        # Model attributes
        assert Model._auto_create_table is True
        assert Model._transient is False


class TestAbstractModel:
    """Test AbstractModel functionality"""

    def test_abstract_model_creation(self):
        """Test that AbstractModel can be subclassed but not used for CRUD"""
        
        class TestAbstractModel(AbstractModel):
            _name = 'test.abstract'
            _description = 'Test Abstract Model'
            
            test_field = Char(string='Test Field')
        
        # Should be able to create the class
        assert TestAbstractModel._name == 'test.abstract'
        assert TestAbstractModel._abstract is True
        assert TestAbstractModel._auto_create_table is False

    @pytest.mark.asyncio
    async def test_abstract_model_crud_operations_fail(self):
        """Test that AbstractModel CRUD operations raise NotImplementedError"""
        
        class TestAbstractModel(AbstractModel):
            _name = 'test.abstract'
            _description = 'Test Abstract Model'
        
        # Create should fail
        with pytest.raises(NotImplementedError, match="Cannot create records for abstract model"):
            await TestAbstractModel.create({'name': 'Test'})
        
        # Search should fail
        with pytest.raises(NotImplementedError, match="Cannot search records for abstract model"):
            await TestAbstractModel.search([])
        
        # Browse should fail
        with pytest.raises(NotImplementedError, match="Cannot browse records for abstract model"):
            await TestAbstractModel.browse(['test-id'])


class TestTransientModel:
    """Test TransientModel functionality"""

    def test_transient_model_creation(self):
        """Test that TransientModel can be created with proper attributes"""
        
        class TestTransientModel(TransientModel):
            _name = 'test.transient'
            _description = 'Test Transient Model'
            
            test_field = Char(string='Test Field')
        
        # Should have transient attributes
        assert TestTransientModel._name == 'test.transient'
        assert TestTransientModel._transient is True
        assert TestTransientModel._auto_create_table is True
        assert TestTransientModel._transient_max_hours == 1

    def test_transient_model_custom_cleanup_time(self):
        """Test that TransientModel can have custom cleanup time"""
        
        class CustomTransientModel(TransientModel):
            _name = 'test.custom.transient'
            _description = 'Custom Transient Model'
            _transient_max_hours = 24  # 24 hours instead of default 1
        
        assert CustomTransientModel._transient_max_hours == 24

    @pytest.mark.asyncio
    async def test_transient_model_cleanup_method(self):
        """Test the cleanup method for transient models"""
        
        class TestTransientModel(TransientModel):
            _name = 'test.transient'
            _description = 'Test Transient Model'
            _transient_max_hours = 1
        
        # Mock the search and unlink methods
        with patch.object(TestTransientModel, 'search') as mock_search, \
             patch.object(TestTransientModel, 'unlink') as mock_unlink:
            
            # Mock old records
            mock_old_records = MagicMock()
            mock_old_records.__len__ = MagicMock(return_value=5)
            mock_search.return_value = mock_old_records
            
            # Call cleanup
            await TestTransientModel._cleanup_transient_records()
            
            # Verify search was called with correct domain
            mock_search.assert_called_once()
            call_args = mock_search.call_args[0][0]  # Get the domain argument
            assert len(call_args) == 1
            assert call_args[0][0] == 'createAt'
            assert call_args[0][1] == '<'
            # The timestamp should be approximately 1 hour ago
            cutoff_time = call_args[0][2]
            expected_cutoff = datetime.now() - timedelta(hours=1)
            assert abs((cutoff_time - expected_cutoff).total_seconds()) < 60  # Within 1 minute
            
            # Verify unlink was called
            mock_old_records.unlink.assert_called_once()


class TestModel:
    """Test standard Model functionality"""

    def test_model_creation(self):
        """Test that Model can be created with proper attributes"""
        
        class TestModel(Model):
            _name = 'test.model'
            _description = 'Test Model'
            
            test_field = Char(string='Test Field')
        
        # Should have standard model attributes
        assert TestModel._name == 'test.model'
        assert TestModel._auto_create_table is True
        assert TestModel._transient is False
        assert not hasattr(TestModel, '_abstract') or TestModel._abstract is False

    def test_model_inherits_common_fields(self):
        """Test that Model inherits common fields from BaseModel"""
        
        class TestModel(Model):
            _name = 'test.model'
            _description = 'Test Model'
        
        # Should have inherited fields
        assert 'id' in TestModel._fields
        assert 'name' in TestModel._fields
        assert 'createAt' in TestModel._fields
        assert 'updateAt' in TestModel._fields

    def test_model_can_add_custom_fields(self):
        """Test that Model can add custom fields"""
        
        class TestModel(Model):
            _name = 'test.model'
            _description = 'Test Model'
            
            custom_field = Char(string='Custom Field')
            active = Boolean(string='Active', default=True)
        
        # Should have both inherited and custom fields
        assert 'id' in TestModel._fields
        assert 'name' in TestModel._fields
        assert 'custom_field' in TestModel._fields
        assert 'active' in TestModel._fields


class TestModelRegistry:
    """Test that model registry properly handles the hierarchy"""

    def test_registry_filtering_logic(self):
        """Test that registry has proper filtering logic for model types"""
        from erp.models.registry import ModelRegistry

        registry = ModelRegistry('test_addon')

        # Test that the registry has the get_models_by_type method
        assert hasattr(registry, 'get_models_by_type')

        # Test that it can handle different model type filters
        # (We can't easily test the actual filtering without real models,
        # but we can test that the method exists and accepts the right parameters)
        try:
            standard_models = registry.get_models_by_type('standard')
            abstract_models = registry.get_models_by_type('abstract')
            transient_models = registry.get_models_by_type('transient')
            all_models = registry.get_models_by_type('all')

            # These should all return dictionaries (even if empty)
            assert isinstance(standard_models, dict)
            assert isinstance(abstract_models, dict)
            assert isinstance(transient_models, dict)
            assert isinstance(all_models, dict)
        except Exception as e:
            pytest.fail(f"Registry filtering failed: {e}")


if __name__ == '__main__':
    pytest.main([__file__])
